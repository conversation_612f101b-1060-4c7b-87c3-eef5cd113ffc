---
services_info:
    restart:
        # 默认配置
        defaults:
            remote_logs_dir: "/mnt/efs/production/devops/logs/ansible-service-restart-logs"
            execution_timeout: -1 # -1表示无超时限制，其他数值为超时秒数
            max_retry_attempts: 3
            retry_delay: 10 # 重试间隔10秒

        # Tag到服务名称的映射表（用于tag-based服务重启）
        tag_to_service_mapping:
            service_async_merge: async-merge
            service_async_dispatcher: async-dispatcher
            service_med_queue: med-queue
            service_async_recognize: async-recognize
            service_selector: selector
            service_php_fpm_74: php-fpm-74
            service_nginx: nginx
            service_bureau_queue: bureau-queue
            service_med_cover_original: med-cover-original
            service_prescription: prescription
            service_smart_queue: smart-queue

        # 所有服务的统一配置定义（避免重复定义）
        services:
            async-merge:
                host: ***********
                path: /mnt/efs/production/www/medical-docker
                command: docker-compose restart async-merge
                tags:
                    - service_async_merge
            async-dispatcher:
                host: ***********
                path: /mnt/efs/production/www/medical-docker
                command: docker-compose restart async-dispatcher
                tags:
                    - service_async_dispatcher
            med-queue:
                host: ***********
                path: /mnt/efs/production/www/medical-docker
                command: docker-compose restart med-queue
                tags:
                    - service_med_queue
            async-recognize:
                host: ************
                path: /mnt/efs/production/www/medical-docker
                command: docker-compose restart async-recognize
                tags:
                    - service_async_recognize
            selector:
                host: ***********
                path: /mnt/efs/production/selector/medical-docker
                command: docker-compose restart selector
                tags:
                    - service_selector
            php-fpm-74:
                host: ***********
                path: /home/<USER>/medical-docker
                command: docker-compose restart php-fpm-74
                tags:
                    - service_php_fpm_74
            nginx:
                host: ***********
                path: /home/<USER>/medical-docker
                command: docker-compose restart nginx
                tags:
                    - service_nginx
            bureau-queue:
                host: ***********
                path: /mnt/efs/production/bureau/medical-docker
                command: docker-compose restart bureau-queue
                tags:
                    - service_bureau_queue
            med-cover-original:
                host: ***********
                path: /mnt/efs/production/bureau/medical-docker
                command: docker-compose restart med-cover-original
                tags:
                    - service_med_cover_original
            prescription:
                host: ***********
                path: /mnt/efs/production/bureau/medical-docker
                command: docker-compose restart prescription
                tags:
                    - service_prescription
            smart-queue:
                host: ***********
                path: /mnt/efs/production/bureau/medical-docker
                command: docker-compose restart smart-queue
                tags:
                    - service_smart_queue

        # 按发布端分组的服务映射（引用上面的服务定义）
        groups:
            recognize:
                - async-merge
                - async-dispatcher
                - med-queue
                - async-recognize
            qps:
                - selector
            api:
                - php-fpm-74
                - nginx
                - med-queue
            bureau:
                - bureau-queue
                - med-cover-original
                - prescription
            smart:
                - smart-queue
