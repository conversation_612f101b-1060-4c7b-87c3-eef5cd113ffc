---
# Test playbook to verify the fixes for display issues
- name: Test Display Fix Verification
  hosts: localhost
  gather_facts: false
  vars:
    # Test with string format that was causing the character splitting issue
    restart_services: "['med-cover-original','prescription']"
    restart_groups: []
  tasks:
    - name: Load service configuration
      ansible.builtin.include_vars:
        file: roles/04-restart-services/vars/main.yml

    - name: Save original restart_services for display
      ansible.builtin.set_fact:
        restart_services_original: "{{ restart_services | default([]) }}"

    - name: Parse restart_groups variable
      ansible.builtin.set_fact:
        restart_groups_parsed: >-
          {%- set raw_groups = restart_groups | default([]) -%}
          {%- if raw_groups is string -%}
          {%- if raw_groups.startswith('[') and raw_groups.endswith(']') -%}
          {{ raw_groups | from_yaml }}{%- else -%}
          {{ [raw_groups] }}{%- endif -%}
          {%- else -%}
          {{ raw_groups }}{%- endif -%}

    - name: Parse restart_services variable
      ansible.builtin.set_fact:
        restart_services_parsed: >-
          {%- set raw_services = restart_services | default([]) -%}
          {%- if raw_services is string -%}
          {%- if raw_services.startswith('[') and raw_services.endswith(']') -%}
          {{ raw_services | from_yaml }}{%- else -%}
          {{ [raw_services] }}{%- endif -%}
          {%- else -%}
          {{ raw_services }}{%- endif -%}

    - name: Detect service tags from ansible_run_tags
      ansible.builtin.set_fact:
        service_tags_detected: >-
          {{
            ansible_run_tags | default([]) |
            select('match', '^service_.*') |
            list
          }}

    - name: Map service tags to service names
      ansible.builtin.set_fact:
        services_from_tags: >-
          {{
            service_tags_detected |
            map('extract', services_info.restart.tag_to_service_mapping) |
            select('defined') |
            list
          }}

    - name: Merge tag-based services with restart_services
      ansible.builtin.set_fact:
        restart_services_parsed: >-
          {{
            (restart_services_parsed | default([])) +
            (services_from_tags | default([])) |
            unique
          }}

    - name: Test display with fixed format
      ansible.builtin.debug:
        msg: |
          Fixed Display Test Results:
          ==========================
          Original restart_services input: {{ restart_services }}
          Original restart_services type: {{ restart_services | type_debug }}
          Saved restart_services_original: {{ restart_services_original }}
          Saved restart_services_original type: {{ restart_services_original | type_debug }}
          
          Groups to restart: {{ restart_groups_parsed | join(', ') if restart_groups_parsed | length > 0 else 'None' }}
          Individual services (from -e): {{ restart_services_original | join(', ') if restart_services_original | length > 0 else 'None' }}
          Service tags detected: {{ service_tags_detected | join(', ') if service_tags_detected | length > 0 else 'None' }}
          Services from tags: {{ services_from_tags | join(', ') if services_from_tags | length > 0 else 'None' }}
          Final services list: {{ restart_services_parsed | join(', ') if restart_services_parsed | length > 0 else 'None' }}
          
          This should only appear once (on localhost only).
