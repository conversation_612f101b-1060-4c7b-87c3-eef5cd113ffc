---
# Test playbook for tag-based service restart functionality
# This playbook can be used to test the new tag-based service restart feature

- name: Test Tag-based Service Restart
  hosts: localhost
  gather_facts: false
  vars:
    # Test variables - you can override these with -e
    restart_services: []
    restart_groups: []
  tasks:
    - name: Load service configuration
      ansible.builtin.include_vars:
        file: roles/04-restart-services/vars/main.yml

    - name: Parse restart_groups variable (handle both array and string formats)
      ansible.builtin.set_fact:
        restart_groups_parsed: >-
          {%- set raw_groups = restart_groups | default([]) -%}
          {%- if raw_groups is string -%}
          {%- if raw_groups.startswith('[') and raw_groups.endswith(']') -%}
          {{ raw_groups | from_yaml }}{%- else -%}
          {{ [raw_groups] }}{%- endif -%}
          {%- else -%}
          {{ raw_groups }}{%- endif -%}

    - name: Save original restart_services for display
      ansible.builtin.set_fact:
        restart_services_original: "{{ restart_services | default([]) }}"

    - name: Parse restart_services variable (handle both array and string formats)
      ansible.builtin.set_fact:
        restart_services_parsed: >-
          {%- set raw_services = restart_services | default([]) -%}
          {%- if raw_services is string -%}
          {%- if raw_services.startswith('[') and raw_services.endswith(']') -%}
          {{ raw_services | from_yaml }}{%- else -%}
          {{ [raw_services] }}{%- endif -%}
          {%- else -%}
          {{ raw_services }}{%- endif -%}

    # Detect and collect services based on tags
    - name: Detect service tags from ansible_run_tags
      ansible.builtin.set_fact:
        service_tags_detected: >-
          {{
            ansible_run_tags | default([]) |
            select('match', '^service_.*') |
            list
          }}

    - name: Map service tags to service names
      ansible.builtin.set_fact:
        services_from_tags: >-
          {{
            service_tags_detected |
            map('extract', services_info.restart.tag_to_service_mapping) |
            select('defined') |
            list
          }}

    # Merge tag-based services with restart_services
    - name: Merge tag-based services with restart_services
      ansible.builtin.set_fact:
        restart_services_parsed: >-
          {{
            (restart_services_parsed | default([])) +
            (services_from_tags | default([])) |
            unique
          }}

    - name: Display test results
      ansible.builtin.debug:
        msg: |
          Tag-based Service Restart Test Results:
          ======================================
          Current ansible_run_tags: {{ ansible_run_tags | default([]) }}
          Service tags detected: {{ service_tags_detected }}
          Services from tags: {{ services_from_tags }}
          Services from -e restart_services: {{ restart_services_original }}
          Services from -e restart_groups: {{ restart_groups_parsed }}
          Final merged services list: {{ restart_services_parsed }}

          Available service tags:
          {% for tag, service in services_info.restart.tag_to_service_mapping.items() %}
          - {{ tag }} -> {{ service }}
          {% endfor %}
